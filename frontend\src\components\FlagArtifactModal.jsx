import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, TextField } from "@mui/material";
import ModalContainer from "./ModalContainer";
import theme from "../theme";

const FlagArtifactModal = ({ initialState, onClose, onConfirm, isLoading = false }) => {
    const [reason, setReason] = useState("");
    const [error, setError] = useState("");

    const handleSubmit = (e) => {
        setError("");
        
        // Validate reason length
        if (reason.length > 500) {
            setError("Reason must be 500 characters or less");
            return;
        }

        onConfirm(e, reason.trim() || null);
    };

    const handleClose = (e) => {
        setReason("");
        setError("");
        onClose(e);
    };

    const handleReasonChange = (e) => {
        setReason(e.target.value);
        if (error) setError("");
    };

    return (
        <Modal open={initialState} onClose={handleClose}>
            <ModalContainer title="Flag Artifact" onClose={handleClose} headerPosition="center">
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: 400 }}>
                    <Grid>
                        <Typography fontWeight={"100"} marginBottom={2}>
                            Why are you flagging this artifact? This will help administrators review the content.
                        </Typography>
                        <TextField
                            fullWidth
                            multiline
                            rows={4}
                            placeholder="Enter reason for flagging (optional)"
                            value={reason}
                            onChange={handleReasonChange}
                            onClick={(e) => e.stopPropagation()}
                            error={!!error}
                            helperText={error || `${reason.length}/500 characters`}
                            disabled={isLoading}
                            sx={{
                                "& .MuiOutlinedInput-root": {
                                    color: "white",
                                    "& fieldset": {
                                        borderColor: theme.palette.custom.borderColor,
                                    },
                                    "&:hover fieldset": {
                                        borderColor: theme.palette.primary.main,
                                    },
                                    "&.Mui-focused fieldset": {
                                        borderColor: theme.palette.primary.main,
                                    },
                                },
                                "& .MuiInputLabel-root": {
                                    color: "white",
                                },
                                "& .MuiFormHelperText-root": {
                                    color: error ? theme.palette.error.main : "rgba(255, 255, 255, 0.7)",
                                },
                            }}
                        />
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button
                                variant="contained"
                                sx={{ 
                                    background: "#FFFFFF !important", 
                                    color: theme.palette.primary.main 
                                }}
                                onClick={handleClose}
                                disabled={isLoading}
                            >
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button 
                                variant="contained" 
                                onClick={handleSubmit}
                                disabled={isLoading || !!error}
                                color="warning"
                            >
                                {isLoading ? "Flagging..." : "Flag Artifact"}
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default FlagArtifactModal;
