import axiosInstance from "../axios";

class ArtifactFlagController {
    /**
     * Flag an artifact
     * @param {string} artifactId - The ID of the artifact to flag
     * @param {string} reason - Optional reason for flagging
     * @returns {Promise<Object>} Response data
     */
    async flagArtifact(artifactId, reason = null) {
        try {
            const response = await axiosInstance.post(`/artifacts/${artifactId}/flag`, {
                reason: reason?.trim() || null,
            });
            return response.data;
        } catch (error) {
            console.error("Error flagging artifact:", error);
            throw error;
        }
    }

    /**
     * Get flagged artifacts
     * @param {string} status - Filter by status: 'pending' or 'approved'
     * @returns {Promise<Array>} Array of flagged artifacts
     */
    async getFlaggedArtifacts(status = "pending") {
        try {
            const response = await axiosInstance.get(`/artifacts/flagged?status=${status}`);
            return response.data.artifacts || [];
        } catch (error) {
            console.error("Error fetching flagged artifacts:", error);
            throw error;
        }
    }

    /**
     * Approve a flag (archives the artifact)
     * @param {string} flagId - The ID of the flag to approve
     * @returns {Promise<Object>} Response data
     */
    async approveFlag(flagId) {
        try {
            const response = await axiosInstance.post(`/artifacts/flags/${flagId}/approve`);
            return response.data;
        } catch (error) {
            console.error("Error approving flag:", error);
            throw error;
        }
    }

    /**
     * Check if user has already flagged an artifact
     * @param {string} artifactId - The ID of the artifact
     * @param {Array} userFlags - Array of user's flags
     * @returns {boolean} True if user has flagged this artifact
     */
    hasUserFlaggedArtifact(artifactId, userFlags = []) {
        return userFlags.some(flag => flag.artifactId === artifactId);
    }

    /**
     * Get flag statistics for an artifact
     * @param {Object} artifact - The artifact object with flags
     * @returns {Object} Flag statistics
     */
    getFlagStatistics(artifact) {
        if (!artifact.flags || !Array.isArray(artifact.flags)) {
            return {
                totalFlags: 0,
                pendingFlags: 0,
                approvedFlags: 0,
                flaggedByUsers: [],
            };
        }

        const pendingFlags = artifact.flags.filter(flag => !flag.approvedBy);
        const approvedFlags = artifact.flags.filter(flag => flag.approvedBy);
        const flaggedByUsers = artifact.flags.map(flag => ({
            userId: flag.flaggedBy,
            userName: flag.flaggedByUser?.name || "Unknown User",
            reason: flag.reason,
            flaggedAt: flag.flaggedAt,
        }));

        return {
            totalFlags: artifact.flags.length,
            pendingFlags: pendingFlags.length,
            approvedFlags: approvedFlags.length,
            flaggedByUsers,
        };
    }

    /**
     * Format flag data for display
     * @param {Object} flag - The flag object
     * @returns {Object} Formatted flag data
     */
    formatFlagForDisplay(flag) {
        return {
            id: flag._id,
            reason: flag.reason || "No reason provided",
            flaggedBy: flag.flaggedByUser?.name || "Unknown User",
            flaggedAt: new Date(flag.flaggedAt),
            approvedBy: flag.approvedByUser?.name || null,
            approvedAt: flag.approvedAt ? new Date(flag.approvedAt) : null,
            isApproved: !!flag.approvedBy,
        };
    }

    /**
     * Group flags by user for display
     * @param {Array} flags - Array of flags
     * @returns {Array} Grouped flags by user
     */
    groupFlagsByUser(flags) {
        const userFlagsMap = new Map();

        flags.forEach(flag => {
            const userId = flag.flaggedBy;
            const userName = flag.flaggedByUser?.name || "Unknown User";
            
            if (!userFlagsMap.has(userId)) {
                userFlagsMap.set(userId, {
                    userId,
                    userName,
                    flags: [],
                    totalFlags: 0,
                });
            }

            const userFlags = userFlagsMap.get(userId);
            userFlags.flags.push(this.formatFlagForDisplay(flag));
            userFlags.totalFlags++;
        });

        return Array.from(userFlagsMap.values()).sort((a, b) => b.totalFlags - a.totalFlags);
    }
}

const artifactFlagController = new ArtifactFlagController();
export default artifactFlagController;
