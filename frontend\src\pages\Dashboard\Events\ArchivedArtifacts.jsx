import { Grid, Tab, Tabs, Box } from "@mui/material";
import { useEffect, useState, memo, useRef } from "react";
import DetailModal from "./DetailModal";
import { useParams } from "react-router-dom";
import theme from "../../../theme";
import VirtualizedCardList from "./VirtualizedCardList";
import FlaggedArtifacts from "./FlaggedArtifacts";
import axiosInstance from "../../../axios";

const ArchivedArtifacts = ({ vessels }) => {
    const { id } = useParams();
    const [tab, setTab] = useState("archived");
    const [events, setEvents] = useState([]);
    const [filteredEvents, setFilteredEvents] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState(null);
    const virtualizedListContainerRef = useRef();

    const fetchArtifacts = async () => {
        setIsLoading(true);
        try {
            const res = await axiosInstance.get("/artifacts/archived");
            setEvents(res.data.artifacts || []);
            setIsLoading(false);
        } catch (err) {
            setIsLoading(false);
            console.error("Error fetching archived artifacts", err);
        }
    };

    useEffect(() => {
        const unidIds = vessels.map((v) => v.unit_id);
        setFilteredEvents(events.filter((e) => unidIds.includes(e.unit_id)));
    }, [events, vessels]);

    useEffect(() => {
        fetchArtifacts();
    }, [id]);

    useEffect(() => {
        const refetch = () => fetchArtifacts();
        window.addEventListener("artifact/changed", refetch);
        return () => window.removeEventListener("artifact/changed", refetch);
    }, []);

    const handleTabChange = (event, newValue) => {
        setTab(newValue);
    };

    const tabs = [
        { value: "archived", label: "Archived" },
        { value: "flagged", label: "Flagged" },
    ];

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid container marginBottom={2}>
                <Tabs
                    value={tab}
                    onChange={handleTabChange}
                    sx={{
                        "& .MuiTabs-indicator": {
                            backgroundColor: theme.palette.primary.main,
                        },
                        "& .MuiTab-root": {
                            color: "rgba(255, 255, 255, 0.7)",
                            "&.Mui-selected": {
                                color: theme.palette.primary.main,
                            },
                        },
                    }}
                >
                    {tabs.map((tabItem) => (
                        <Tab
                            key={tabItem.value}
                            label={tabItem.label}
                            value={tabItem.value}
                        />
                    ))}
                </Tabs>
            </Grid>

            {tab === "archived" ? (
                <Grid
                    container
                    overflow={"auto"}
                    display={"block"}
                    border={`1px solid ${theme.palette.custom.borderColor}`}
                    borderRadius={"10px"}
                    padding={"10px 24px"}
                    size="grow"
                >
                    <Grid container height={"100%"} overflow={"auto"} ref={virtualizedListContainerRef}>
                        <VirtualizedCardList
                            events={filteredEvents}
                            setShowDetailModal={setShowDetailModal}
                            setSelectedCard={setSelectedCard}
                            favouriteArtifacts={[]}
                            isLoading={isLoading}
                            containerRef={virtualizedListContainerRef}
                        />
                    </Grid>
                </Grid>
            ) : (
                <FlaggedArtifacts vessels={vessels} />
            )}

            <DetailModal
                showDetailModal={showDetailModal}
                setShowDetailModal={setShowDetailModal}
                selectedCard={selectedCard}
                setSelectedCard={setSelectedCard}
                id={id}
                favouriteArtifacts={[]}
            />
        </Grid>
    );
};

export default memo(ArchivedArtifacts);
