import React, { useState, useEffect, useRef } from "react";
import { <PERSON>rid, Tab, Ta<PERSON>, <PERSON><PERSON><PERSON>, Button, Chip, Box, Tooltip, Card, CardContent, Avatar, CircularProgress } from "@mui/material";
import { useParams } from "react-router-dom";
import { format } from "date-fns";
import FlagIcon from "@mui/icons-material/Flag";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import theme from "../../../theme";
import VirtualizedCardList from "./VirtualizedCardList";
import DetailModal from "./DetailModal";
import PreviewMedia from "../../../components/PreviewMedia";
import axiosInstance from "../../../axios";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";
import { useUser } from "../../../hooks/UserHook";
import { permissions, displayCoordinates } from "../../../utils";
import { useApp } from "../../../hooks/AppHook";
import useVesselInfo from "../../../hooks/VesselInfoHook";

const FlaggedArtifacts = ({ vessels }) => {
    const { id } = useParams();
    const { user } = useUser();
    const [tab, setTab] = useState("pending");
    const [pendingArtifacts, setPendingArtifacts] = useState([]);
    const [approvedArtifacts, setApprovedArtifacts] = useState([]);
    const [filteredArtifacts, setFilteredArtifacts] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState(null);
    const [approvingFlags, setApprovingFlags] = useState(new Set());
    const virtualizedListContainerRef = useRef();

    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);

    const fetchFlaggedArtifacts = async (status) => {
        setIsLoading(true);
        try {
            const artifacts = await artifactFlagController.getFlaggedArtifacts(status);
            return artifacts;
        } catch (err) {
            console.error(`Error fetching ${status} flagged artifacts:`, err);
            return [];
        } finally {
            setIsLoading(false);
        }
    };

    const loadArtifacts = async () => {
        const [pending, approved] = await Promise.all([
            fetchFlaggedArtifacts("pending"),
            fetchFlaggedArtifacts("approved")
        ]);
        setPendingArtifacts(pending);
        setApprovedArtifacts(approved);
    };

    const handleTabChange = (event, newValue) => {
        setTab(newValue);
    };

    const handleApproveFlag = async (flagId, artifactId) => {
        setApprovingFlags(prev => new Set(prev).add(flagId));
        try {
            await artifactFlagController.approveFlag(flagId);
            // Refresh the data
            await loadArtifacts();
            window.dispatchEvent(new CustomEvent("artifact/changed", { detail: { artifactId } }));
        } catch (err) {
            console.error("Error approving flag:", err);
        } finally {
            setApprovingFlags(prev => {
                const newSet = new Set(prev);
                newSet.delete(flagId);
                return newSet;
            });
        }
    };

    useEffect(() => {
        if (hasManageArtifacts) {
            loadArtifacts();
        }
    }, [hasManageArtifacts]);

    useEffect(() => {
        const currentArtifacts = tab === "pending" ? pendingArtifacts : approvedArtifacts;
        const unitIds = vessels.map((v) => v.unit_id);
        setFilteredArtifacts(currentArtifacts.filter((e) => unitIds.includes(e.unit_id)));
    }, [tab, pendingArtifacts, approvedArtifacts, vessels]);

    useEffect(() => {
        const refetch = () => loadArtifacts();
        window.addEventListener("artifact/flagged", refetch);
        return () => window.removeEventListener("artifact/flagged", refetch);
    }, []);

    if (!hasManageArtifacts) {
        return (
            <Grid container justifyContent="center" alignItems="center" height="100%">
                <Typography color="white">You don't have permission to view flagged artifacts.</Typography>
            </Grid>
        );
    }

    const tabs = [
        { value: "pending", label: "Pending", count: pendingArtifacts.length },
        { value: "approved", label: "Approved", count: approvedArtifacts.length },
    ];

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid container marginBottom={2}>
                <Tabs
                    value={tab}
                    onChange={handleTabChange}
                    sx={{
                        "& .MuiTabs-indicator": {
                            backgroundColor: theme.palette.primary.main,
                        },
                        "& .MuiTab-root": {
                            color: "rgba(255, 255, 255, 0.7)",
                            "&.Mui-selected": {
                                color: theme.palette.primary.main,
                            },
                        },
                    }}
                >
                    {tabs.map((tabItem) => (
                        <Tab
                            key={tabItem.value}
                            label={
                                <Box display="flex" alignItems="center" gap={1}>
                                    {tabItem.label}
                                    <Chip
                                        label={tabItem.count}
                                        size="small"
                                        sx={{
                                            backgroundColor: tab === tabItem.value ? theme.palette.primary.main : "rgba(255, 255, 255, 0.2)",
                                            color: "white",
                                            fontSize: "0.75rem",
                                        }}
                                    />
                                </Box>
                            }
                            value={tabItem.value}
                        />
                    ))}
                </Tabs>
            </Grid>
            
            <Grid
                container
                overflow={"auto"}
                display={"block"}
                border={`1px solid ${theme.palette.custom.borderColor}`}
                borderRadius={"10px"}
                padding={"10px 24px"}
                size="grow"
            >
                <Grid container height={"100%"} overflow={"auto"} ref={virtualizedListContainerRef}>
                    <FlaggedArtifactsList
                        artifacts={filteredArtifacts}
                        setShowDetailModal={setShowDetailModal}
                        setSelectedCard={setSelectedCard}
                        isLoading={isLoading}
                        containerRef={virtualizedListContainerRef}
                        onApproveFlag={handleApproveFlag}
                        approvingFlags={approvingFlags}
                        showApproveButton={tab === "pending"}
                    />
                </Grid>
            </Grid>
            
            <DetailModal
                showDetailModal={showDetailModal}
                setShowDetailModal={setShowDetailModal}
                selectedCard={selectedCard}
                setSelectedCard={setSelectedCard}
                id={id}
                favouriteArtifacts={[]}
            />
        </Grid>
    );
};

const FlaggedArtifactsList = ({
    artifacts,
    setShowDetailModal,
    setSelectedCard,
    isLoading,
    onApproveFlag,
    approvingFlags,
    showApproveButton
}) => {
    const { timezone } = useApp();
    const { vesselInfo } = useVesselInfo();

    if (isLoading) {
        return (
            <Grid container justifyContent="center" alignItems="center" height="200px">
                <CircularProgress />
            </Grid>
        );
    }

    if (artifacts.length === 0) {
        return (
            <Grid container justifyContent="center" alignItems="center" height="200px">
                <Typography color="rgba(255, 255, 255, 0.7)">
                    No flagged artifacts found
                </Typography>
            </Grid>
        );
    }

    return (
        <Grid container spacing={2}>
            {artifacts.map((artifact) => (
                <FlaggedArtifactCard
                    key={artifact._id}
                    artifact={artifact}
                    setShowDetailModal={setShowDetailModal}
                    setSelectedCard={setSelectedCard}
                    onApproveFlag={onApproveFlag}
                    approvingFlags={approvingFlags}
                    showApproveButton={showApproveButton}
                    timezone={timezone}
                    vesselInfo={vesselInfo}
                />
            ))}
        </Grid>
    );
};

const FlaggedArtifactCard = ({
    artifact,
    setShowDetailModal,
    setSelectedCard,
    onApproveFlag,
    approvingFlags,
    showApproveButton,
    timezone,
    vesselInfo
}) => {
    const vessel = vesselInfo.find((v) => v.vessel_id === artifact.onboard_vessel_id);
    const vesselName = vessel?.name || artifact.unit_id;
    const roundedCoordinates = displayCoordinates(artifact.location?.coordinates, false);

    const handleClick = () => {
        setShowDetailModal(true);
        setSelectedCard({
            ...artifact,
            vesselName,
        });
    };

    return (
        <Grid size={12} marginBottom={2}>
            <Card
                sx={{
                    backgroundColor: theme.palette.primary.dark,
                    border: `1px solid ${theme.palette.custom.borderColor}`,
                    cursor: "pointer",
                    "&:hover": {
                        backgroundColor: "rgba(255, 255, 255, 0.05)",
                    },
                }}
                onClick={handleClick}
            >
                <CardContent>
                    <Grid container spacing={2}>
                        <Grid size={4}>
                            <PreviewMedia
                                cardId={artifact._id}
                                isImage={!artifact.video_path}
                                style={{ borderRadius: 8, height: 200 }}
                                favouriteArtifacts={[]}
                                showVideoThumbnail={!!artifact.video_path}
                                onThumbnailClick={handleClick}
                            />
                        </Grid>
                        <Grid size={8}>
                            <Grid container spacing={1}>
                                <Grid size={12}>
                                    <Typography variant="h6" color="white" gutterBottom>
                                        {vesselName} - {artifact.category || artifact.super_category}
                                    </Typography>
                                    <Typography variant="body2" color="rgba(255, 255, 255, 0.7)">
                                        {format(new Date(artifact.timestamp), "PPpp")} • {roundedCoordinates}
                                    </Typography>
                                </Grid>

                                <Grid size={12} marginTop={1}>
                                    <Typography variant="subtitle2" color="white" gutterBottom>
                                        Flags ({artifact.flagCount}):
                                    </Typography>
                                    {artifact.flags.map((flag, index) => (
                                        <Box key={flag._id} marginBottom={1}>
                                            <Grid container alignItems="center" spacing={1}>
                                                <Grid>
                                                    <Avatar sx={{ width: 24, height: 24, fontSize: "0.75rem" }}>
                                                        {flag.flaggedByUser?.name?.charAt(0) || "U"}
                                                    </Avatar>
                                                </Grid>
                                                <Grid size="grow">
                                                    <Typography variant="body2" color="white">
                                                        <strong>{flag.flaggedByUser?.name || "Unknown User"}</strong>
                                                        {flag.reason && `: "${flag.reason}"`}
                                                    </Typography>
                                                    <Typography variant="caption" color="rgba(255, 255, 255, 0.5)">
                                                        {format(new Date(flag.flaggedAt), "PPpp")}
                                                        {flag.approvedBy && (
                                                            <span> • Approved by {flag.approvedByUser?.name}</span>
                                                        )}
                                                    </Typography>
                                                </Grid>
                                                {showApproveButton && !flag.approvedBy && (
                                                    <Grid>
                                                        <Button
                                                            size="small"
                                                            variant="contained"
                                                            color="success"
                                                            startIcon={approvingFlags.has(flag._id) ? <CircularProgress size={16} /> : <CheckCircleIcon />}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                onApproveFlag(flag._id, artifact._id);
                                                            }}
                                                            disabled={approvingFlags.has(flag._id)}
                                                        >
                                                            {approvingFlags.has(flag._id) ? "Approving..." : "Approve"}
                                                        </Button>
                                                    </Grid>
                                                )}
                                            </Grid>
                                        </Box>
                                    ))}
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>
        </Grid>
    );
};

export default FlaggedArtifacts;
