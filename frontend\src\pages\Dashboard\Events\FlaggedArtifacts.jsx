import { useState, useEffect } from "react";
import {
    <PERSON>rid,
    Tab,
    Ta<PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>ton,
    Chip,
    Box,
    Card,
    CardContent,
    Avatar,
    CircularProgress
} from "@mui/material";
import { useParams } from "react-router-dom";
import { format } from "date-fns";
import FlagIcon from "@mui/icons-material/Flag";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import CategoryIcon from "@mui/icons-material/Category";
import VisibilityIcon from "@mui/icons-material/Visibility";
import theme from "../../../theme";
import DetailModal from "./DetailModal";
import PreviewMedia from "../../../components/PreviewMedia";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";
import { useUser } from "../../../hooks/UserHook";
import { permissions, displayCoordinates } from "../../../utils";
import useVesselInfo from "../../../hooks/VesselInfoHook";

const FlaggedArtifacts = ({ vessels }) => {
    const { id } = useParams();
    const { user } = useUser();
    const [tab, setTab] = useState("pending");
    const [pendingArtifacts, setPendingArtifacts] = useState([]);
    const [approvedArtifacts, setApprovedArtifacts] = useState([]);
    const [filteredArtifacts, setFilteredArtifacts] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState(null);
    const [approvingFlags, setApprovingFlags] = useState(new Set());

    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);

    const fetchFlaggedArtifacts = async (status) => {
        setIsLoading(true);
        try {
            const artifacts = await artifactFlagController.getFlaggedArtifacts(status);
            return artifacts;
        } catch (err) {
            console.error(`Error fetching ${status} flagged artifacts:`, err);
            return [];
        } finally {
            setIsLoading(false);
        }
    };

    const loadArtifacts = async () => {
        const [pending, approved] = await Promise.all([
            fetchFlaggedArtifacts("pending"),
            fetchFlaggedArtifacts("approved")
        ]);
        setPendingArtifacts(pending);
        setApprovedArtifacts(approved);
    };

    const handleTabChange = (_, newValue) => {
        setTab(newValue);
    };

    const handleApproveFlag = async (flagId, artifactId) => {
        setApprovingFlags(prev => new Set(prev).add(flagId));
        try {
            await artifactFlagController.approveFlag(flagId);
            // Refresh the data
            await loadArtifacts();
            window.dispatchEvent(new CustomEvent("artifact/flagged", { detail: { artifactId } }));
        } catch (err) {
            console.error("Error approving flag:", err);
        } finally {
            setApprovingFlags(prev => {
                const newSet = new Set(prev);
                newSet.delete(flagId);
                return newSet;
            });
        }
    };

    useEffect(() => {
        if (hasManageArtifacts) {
            loadArtifacts();
        }
    }, [hasManageArtifacts]);

    useEffect(() => {
        const currentArtifacts = tab === "pending" ? pendingArtifacts : approvedArtifacts;
        const unitIds = vessels.map((v) => v.unit_id);
        setFilteredArtifacts(currentArtifacts.filter((e) => unitIds.includes(e.unit_id)));
    }, [tab, pendingArtifacts, approvedArtifacts, vessels]);

    useEffect(() => {
        const refetch = () => loadArtifacts();
        window.addEventListener("artifact/flagged", refetch);
        return () => window.removeEventListener("artifact/flagged", refetch);
    }, []);

    if (!hasManageArtifacts) {
        return (
            <Grid container justifyContent="center" alignItems="center" height="100%">
                <Typography color="white">You don't have permission to view flagged artifacts.</Typography>
            </Grid>
        );
    }

    const tabs = [
        { value: "pending", label: "Pending", count: pendingArtifacts.length },
        { value: "approved", label: "Approved", count: approvedArtifacts.length },
    ];

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"} spacing={3}>
            {/* Enhanced Tab Header */}
            <Grid container marginBottom={3}>
                <Box
                    sx={{
                        background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, rgba(0,0,0,0.8) 100%)`,
                        borderRadius: "16px",
                        padding: "8px",
                        border: `1px solid ${theme.palette.custom.borderColor}`,
                        boxShadow: "0 8px 32px rgba(0,0,0,0.3)",
                        backdropFilter: "blur(10px)",
                        width: "100%",
                    }}
                >
                    <Tabs
                        value={tab}
                        onChange={handleTabChange}
                        sx={{
                            "& .MuiTabs-indicator": {
                                backgroundColor: theme.palette.primary.main,
                                height: "3px",
                                borderRadius: "2px",
                            },
                            "& .MuiTab-root": {
                                color: "rgba(255, 255, 255, 0.7)",
                                fontWeight: 600,
                                fontSize: "0.95rem",
                                textTransform: "none",
                                minHeight: "56px",
                                borderRadius: "12px",
                                margin: "0 4px",
                                transition: "all 0.3s ease",
                                "&.Mui-selected": {
                                    color: theme.palette.primary.main,
                                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                                },
                                "&:hover": {
                                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                                },
                            },
                        }}
                    >
                        {tabs.map((tabItem) => (
                            <Tab
                                key={tabItem.value}
                                label={
                                    <Box display="flex" alignItems="center" gap={1.5}>
                                        <FlagIcon sx={{ fontSize: "1.1rem" }} />
                                        {tabItem.label}
                                        <Chip
                                            label={tabItem.count}
                                            size="small"
                                            sx={{
                                                backgroundColor: tab === tabItem.value
                                                    ? theme.palette.primary.main
                                                    : "rgba(255, 255, 255, 0.2)",
                                                color: "white",
                                                fontSize: "0.75rem",
                                                fontWeight: 600,
                                                height: "24px",
                                                minWidth: "24px",
                                                "& .MuiChip-label": {
                                                    padding: "0 8px",
                                                },
                                            }}
                                        />
                                    </Box>
                                }
                                value={tabItem.value}
                            />
                        ))}
                    </Tabs>
                </Box>
            </Grid>

            {/* Enhanced Content Area */}
            <Grid container size="grow" overflow="hidden">
                <Box
                    sx={{
                        width: "100%",
                        height: "100%",
                        background: `linear-gradient(135deg, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.6) 100%)`,
                        borderRadius: "16px",
                        border: `1px solid ${theme.palette.custom.borderColor}`,
                        overflow: "hidden",
                        backdropFilter: "blur(10px)",
                        boxShadow: "0 8px 32px rgba(0,0,0,0.2)",
                    }}
                >
                    <FlaggedArtifactsList
                        artifacts={filteredArtifacts}
                        setShowDetailModal={setShowDetailModal}
                        setSelectedCard={setSelectedCard}
                        isLoading={isLoading}
                        onApproveFlag={handleApproveFlag}
                        approvingFlags={approvingFlags}
                        showApproveButton={tab === "pending"}
                        currentTab={tab}
                    />
                </Box>
            </Grid>

            <DetailModal
                showDetailModal={showDetailModal}
                setShowDetailModal={setShowDetailModal}
                selectedCard={selectedCard}
                setSelectedCard={setSelectedCard}
                id={id}
                favouriteArtifacts={[]}
            />
        </Grid>
    );
};

const FlaggedArtifactsList = ({
    artifacts,
    setShowDetailModal,
    setSelectedCard,
    isLoading,
    onApproveFlag,
    approvingFlags,
    showApproveButton,
    currentTab
}) => {
    const { vesselInfo } = useVesselInfo();

    if (isLoading) {
        return (
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "400px",
                    gap: 2
                }}
            >
                <CircularProgress
                    size={48}
                    sx={{
                        color: theme.palette.primary.main,
                        filter: "drop-shadow(0 0 8px rgba(59, 130, 246, 0.5))"
                    }}
                />
                <Typography
                    variant="h6"
                    color="rgba(255, 255, 255, 0.8)"
                    sx={{ fontWeight: 500 }}
                >
                    Loading flagged artifacts...
                </Typography>
            </Box>
        );
    }

    if (artifacts.length === 0) {
        return (
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "400px",
                    gap: 2,
                    textAlign: "center"
                }}
            >
                <FlagIcon
                    sx={{
                        fontSize: "4rem",
                        color: "rgba(255, 255, 255, 0.3)",
                        filter: "drop-shadow(0 0 8px rgba(255, 255, 255, 0.1))"
                    }}
                />
                <Typography
                    variant="h5"
                    color="rgba(255, 255, 255, 0.8)"
                    sx={{ fontWeight: 600 }}
                >
                    No {currentTab} artifacts
                </Typography>
                <Typography
                    variant="body1"
                    color="rgba(255, 255, 255, 0.6)"
                    sx={{ maxWidth: "400px" }}
                >
                    {currentTab === "pending"
                        ? "No artifacts are currently flagged and waiting for review."
                        : "No flagged artifacts have been approved yet."
                    }
                </Typography>
            </Box>
        );
    }

    return (
        <Box
            sx={{
                padding: "24px",
                height: "100%",
                overflow: "auto",
                "&::-webkit-scrollbar": {
                    width: "8px",
                },
                "&::-webkit-scrollbar-track": {
                    background: "rgba(255, 255, 255, 0.1)",
                    borderRadius: "4px",
                },
                "&::-webkit-scrollbar-thumb": {
                    background: theme.palette.primary.main,
                    borderRadius: "4px",
                    "&:hover": {
                        background: theme.palette.primary.light,
                    },
                },
            }}
        >
            <Grid container spacing={3}>
                {artifacts.map((artifact) => (
                    <Grid size={12} key={artifact._id}>
                        <EnhancedArtifactCard
                            artifact={artifact}
                            setShowDetailModal={setShowDetailModal}
                            setSelectedCard={setSelectedCard}
                            onApproveFlag={onApproveFlag}
                            approvingFlags={approvingFlags}
                            showApproveButton={showApproveButton}
                            vesselInfo={vesselInfo}
                        />
                    </Grid>
                ))}
            </Grid>
        </Box>
    );
};

const EnhancedArtifactCard = ({
    artifact,
    setShowDetailModal,
    setSelectedCard,
    onApproveFlag,
    approvingFlags,
    showApproveButton,
    vesselInfo
}) => {
    const vessel = vesselInfo.find((v) => v.vessel_id === artifact.onboard_vessel_id);
    const vesselName = vessel?.name || artifact.unit_id;
    const roundedCoordinates = displayCoordinates(artifact.location?.coordinates, false);

    const handleClick = () => {
        setShowDetailModal(true);
        setSelectedCard({
            ...artifact,
            vesselName,
        });
    };

    const getStatusColor = () => {
        if (artifact.flags.some(flag => flag.approvedBy)) {
            return "#10B981"; // Green for approved
        }
        return "#F59E0B"; // Orange for pending
    };

    const getStatusText = () => {
        if (artifact.flags.some(flag => flag.approvedBy)) {
            return "Approved";
        }
        return "Pending Review";
    };

    return (
        <Card
            sx={{
                background: `linear-gradient(135deg,
                    rgba(255, 255, 255, 0.1) 0%,
                    rgba(255, 255, 255, 0.05) 100%)`,
                backdropFilter: "blur(10px)",
                border: `1px solid rgba(255, 255, 255, 0.2)`,
                borderRadius: "20px",
                cursor: "pointer",
                transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                position: "relative",
                overflow: "hidden",
                "&:hover": {
                    transform: "translateY(-4px)",
                    boxShadow: "0 20px 40px rgba(0,0,0,0.3)",
                    border: `1px solid ${theme.palette.primary.main}`,
                    "& .artifact-image": {
                        transform: "scale(1.05)",
                    },
                    "& .flag-status": {
                        transform: "scale(1.1)",
                    },
                },
                "&::before": {
                    content: '""',
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    height: "4px",
                    background: `linear-gradient(90deg, ${getStatusColor()}, ${theme.palette.primary.main})`,
                    borderRadius: "20px 20px 0 0",
                },
            }}
            onClick={handleClick}
        >
            <CardContent sx={{ padding: "24px" }}>
                <Grid container spacing={3} alignItems="stretch">
                    {/* Image Section */}
                    <Grid size={4}>
                        <Box
                            sx={{
                                position: "relative",
                                borderRadius: "16px",
                                overflow: "hidden",
                                height: "240px",
                                background: "rgba(0,0,0,0.3)",
                                border: "1px solid rgba(255, 255, 255, 0.1)",
                            }}
                        >
                            <PreviewMedia
                                cardId={artifact._id}
                                isImage={!artifact.video_path}
                                style={{
                                    borderRadius: 16,
                                    height: "100%",
                                    width: "100%",
                                    objectFit: "cover"
                                }}
                                favouriteArtifacts={[]}
                                showVideoThumbnail={!!artifact.video_path}
                                onThumbnailClick={handleClick}
                                className="artifact-image"
                            />

                            {/* Status Badge */}
                            <Chip
                                label={getStatusText()}
                                className="flag-status"
                                sx={{
                                    position: "absolute",
                                    top: 12,
                                    right: 12,
                                    backgroundColor: getStatusColor(),
                                    color: "white",
                                    fontWeight: 600,
                                    fontSize: "0.75rem",
                                    height: "28px",
                                    boxShadow: "0 4px 12px rgba(0,0,0,0.3)",
                                    transition: "transform 0.3s ease",
                                    "& .MuiChip-label": {
                                        padding: "0 12px",
                                    },
                                }}
                            />
                        </Box>
                    </Grid>

                    {/* Content Section */}
                    <Grid size={8}>
                        <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
                            {/* Header */}
                            <Box sx={{ marginBottom: 2 }}>
                                <Typography
                                    variant="h5"
                                    sx={{
                                        color: "white",
                                        fontWeight: 700,
                                        marginBottom: 1,
                                        background: `linear-gradient(135deg, ${theme.palette.primary.main}, #60A5FA)`,
                                        backgroundClip: "text",
                                        WebkitBackgroundClip: "text",
                                        WebkitTextFillColor: "transparent",
                                    }}
                                >
                                    {vesselName}
                                </Typography>

                                <Box sx={{ display: "flex", alignItems: "center", gap: 2, flexWrap: "wrap" }}>
                                    <Chip
                                        icon={<CategoryIcon sx={{ fontSize: "1rem" }} />}
                                        label={artifact.category || artifact.super_category}
                                        size="small"
                                        sx={{
                                            backgroundColor: "rgba(59, 130, 246, 0.2)",
                                            color: theme.palette.primary.light,
                                            border: `1px solid ${theme.palette.primary.main}`,
                                            fontWeight: 500,
                                        }}
                                    />

                                    <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                                        <AccessTimeIcon sx={{ fontSize: "1rem", color: "rgba(255, 255, 255, 0.6)" }} />
                                        <Typography variant="caption" color="rgba(255, 255, 255, 0.6)">
                                            {format(new Date(artifact.timestamp), "MMM dd, yyyy 'at' HH:mm")}
                                        </Typography>
                                    </Box>

                                    <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                                        <LocationOnIcon sx={{ fontSize: "1rem", color: "rgba(255, 255, 255, 0.6)" }} />
                                        <Typography variant="caption" color="rgba(255, 255, 255, 0.6)">
                                            {roundedCoordinates}
                                        </Typography>
                                    </Box>
                                </Box>
                            </Box>

                            {/* Flags Section */}
                            <Box sx={{ flex: 1 }}>
                                <Box sx={{ display: "flex", alignItems: "center", gap: 1, marginBottom: 2 }}>
                                    <FlagIcon sx={{ color: theme.palette.primary.main, fontSize: "1.2rem" }} />
                                    <Typography
                                        variant="subtitle1"
                                        sx={{
                                            color: "white",
                                            fontWeight: 600,
                                        }}
                                    >
                                        Flags ({artifact.flagCount})
                                    </Typography>
                                </Box>

                                <Box sx={{ maxHeight: "120px", overflow: "auto", paddingRight: 1 }}>
                                    {artifact.flags.map((flag) => (
                                        <Box
                                            key={flag._id}
                                            sx={{
                                                marginBottom: 2,
                                                padding: "12px",
                                                background: "rgba(255, 255, 255, 0.05)",
                                                borderRadius: "12px",
                                                border: "1px solid rgba(255, 255, 255, 0.1)",
                                            }}
                                        >
                                            <Box sx={{ display: "flex", alignItems: "flex-start", gap: 1.5 }}>
                                                <Avatar
                                                    sx={{
                                                        width: 32,
                                                        height: 32,
                                                        fontSize: "0.875rem",
                                                        backgroundColor: theme.palette.primary.main,
                                                        color: "white",
                                                        fontWeight: 600,
                                                    }}
                                                >
                                                    {flag.flaggedByUser?.name?.charAt(0) || "U"}
                                                </Avatar>

                                                <Box sx={{ flex: 1, minWidth: 0 }}>
                                                    <Typography
                                                        variant="body2"
                                                        sx={{
                                                            color: "white",
                                                            fontWeight: 600,
                                                            marginBottom: 0.5,
                                                        }}
                                                    >
                                                        {flag.flaggedByUser?.name || "Unknown User"}
                                                    </Typography>

                                                    {flag.reason && (
                                                        <Typography
                                                            variant="body2"
                                                            sx={{
                                                                color: "rgba(255, 255, 255, 0.8)",
                                                                fontStyle: "italic",
                                                                marginBottom: 0.5,
                                                                wordBreak: "break-word",
                                                            }}
                                                        >
                                                            "{flag.reason}"
                                                        </Typography>
                                                    )}

                                                    <Typography
                                                        variant="caption"
                                                        sx={{
                                                            color: "rgba(255, 255, 255, 0.5)",
                                                            display: "block",
                                                        }}
                                                    >
                                                        {format(new Date(flag.flaggedAt), "MMM dd, yyyy 'at' HH:mm")}
                                                        {flag.approvedBy && (
                                                            <>
                                                                <br />
                                                                <CheckCircleIcon sx={{ fontSize: "0.75rem", marginRight: 0.5, verticalAlign: "middle" }} />
                                                                Approved by {flag.approvedByUser?.name}
                                                            </>
                                                        )}
                                                    </Typography>
                                                </Box>

                                                {showApproveButton && !flag.approvedBy && (
                                                    <Button
                                                        size="small"
                                                        variant="contained"
                                                        sx={{
                                                            backgroundColor: "#10B981",
                                                            color: "white",
                                                            fontWeight: 600,
                                                            borderRadius: "8px",
                                                            textTransform: "none",
                                                            minWidth: "100px",
                                                            "&:hover": {
                                                                backgroundColor: "#059669",
                                                                transform: "translateY(-1px)",
                                                                boxShadow: "0 4px 12px rgba(16, 185, 129, 0.4)",
                                                            },
                                                            "&:disabled": {
                                                                backgroundColor: "rgba(16, 185, 129, 0.5)",
                                                            },
                                                        }}
                                                        startIcon={
                                                            approvingFlags.has(flag._id) ?
                                                                <CircularProgress size={16} sx={{ color: "white" }} /> :
                                                                <CheckCircleIcon sx={{ fontSize: "1rem" }} />
                                                        }
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            onApproveFlag(flag._id, artifact._id);
                                                        }}
                                                        disabled={approvingFlags.has(flag._id)}
                                                    >
                                                        {approvingFlags.has(flag._id) ? "Approving..." : "Approve"}
                                                    </Button>
                                                )}
                                            </Box>
                                        </Box>
                                    ))}
                                </Box>
                            </Box>

                            {/* View Details Button */}
                            <Box sx={{ marginTop: 2, display: "flex", justifyContent: "flex-end" }}>
                                <Button
                                    variant="outlined"
                                    startIcon={<VisibilityIcon />}
                                    sx={{
                                        color: theme.palette.primary.main,
                                        borderColor: theme.palette.primary.main,
                                        textTransform: "none",
                                        fontWeight: 600,
                                        borderRadius: "8px",
                                        "&:hover": {
                                            backgroundColor: `${theme.palette.primary.main}20`,
                                            borderColor: theme.palette.primary.light,
                                        },
                                    }}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleClick();
                                    }}
                                >
                                    View Details
                                </Button>
                            </Box>
                        </Box>
                    </Grid>
                </Grid>
            </CardContent>
        </Card>
    );
};

export default FlaggedArtifacts;
