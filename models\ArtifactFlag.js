const mongoose = require("mongoose");
const db = require("../modules/db");

const artifactFlaggedSchema = new mongoose.Schema({
    artifactId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        index: true,
    },
    reason: {
        type: String,
        required: false,
        maxlength: 500,
    },
    flaggedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    flaggedAt: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
    approvedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: false,
        default: null,
    },
    approvedAt: {
        type: Date,
        required: false,
        default: null,
    },
});

// Compound index for efficient queries
artifactFlaggedSchema.index({ artifactId: 1, flaggedBy: 1 });
artifactFlaggedSchema.index({ approvedBy: 1, approvedAt: 1 });
artifactFlaggedSchema.index({ flaggedAt: -1 });

const ArtifactFlagged = db.qm.model("ArtifactFlagged", artifactFlaggedSchema, "artifacts_flagged");

module.exports = ArtifactFlagged;
