const mongoose = require("mongoose");
const db = require("../modules/db");

const artifactFlagSchema = new mongoose.Schema({
    artifactId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        index: true,
    },
    reason: {
        type: String,
        required: false,
        maxlength: 500,
    },
    flaggedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    flaggedAt: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
    approvedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: false,
        default: null,
    },
    approvedAt: {
        type: Date,
        required: false,
        default: null,
    },
});

// Compound index for efficient queries
artifactFlagSchema.index({ artifactId: 1, flaggedBy: 1 });
artifactFlagSchema.index({ approvedBy: 1, approvedAt: 1 });
artifactFlagSchema.index({ flaggedAt: -1 });

const ArtifactFlag = db.qm.model("ArtifactFlag", artifactFlagSchema, "artifact_flags");

module.exports = ArtifactFlag;
