const mongoose = require("mongoose");
const ArtifactFlag = require("../models/ArtifactFlag");
const db = require("../modules/db");

class ArtifactFlagService {
    /**
     * Flag an artifact
     * @param {string} artifactId - The ID of the artifact to flag
     * @param {string} userId - The ID of the user flagging the artifact
     * @param {string} reason - Optional reason for flagging
     * @returns {Promise<Object>} The created flag
     */
    async flagArtifact(artifactId, userId, reason = null) {
        // Check if artifact exists
        const artifact = await db.qmDev.collection("analysis_results").findOne({ 
            _id: mongoose.Types.ObjectId(artifactId) 
        });
        if (!artifact) {
            throw new Error("Artifact not found");
        }

        // Check if user already flagged this artifact
        const existingFlag = await ArtifactFlag.findOne({
            artifactId: mongoose.Types.ObjectId(artifactId),
            flaggedBy: mongoose.Types.ObjectId(userId)
        });
        if (existingFlag) {
            throw new Error("You have already flagged this artifact");
        }

        // Create new flag
        const flag = new ArtifactFlag({
            artifactId: mongoose.Types.ObjectId(artifactId),
            reason: reason || null,
            flaggedBy: mongoose.Types.ObjectId(userId),
        });

        await flag.save();
        return flag;
    }

    /**
     * Get flagged artifacts with aggregated data
     * @param {string} status - Filter by status: 'pending' or 'approved'
     * @returns {Promise<Array>} Array of flagged artifacts with flag details
     */
    async getFlaggedArtifacts(status = "pending") {
        let matchCondition = {};
        if (status === "pending") {
            matchCondition = { approvedBy: null };
        } else if (status === "approved") {
            matchCondition = { approvedBy: { $ne: null } };
        }

        const flaggedArtifacts = await ArtifactFlag.aggregate([
            { $match: matchCondition },
            {
                $lookup: {
                    from: "users",
                    localField: "flaggedBy",
                    foreignField: "_id",
                    as: "flaggedByUser",
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "approvedBy",
                    foreignField: "_id",
                    as: "approvedByUser",
                },
            },
            {
                $group: {
                    _id: "$artifactId",
                    flags: {
                        $push: {
                            _id: "$_id",
                            reason: "$reason",
                            flaggedBy: "$flaggedBy",
                            flaggedByUser: { $arrayElemAt: ["$flaggedByUser", 0] },
                            flaggedAt: "$flaggedAt",
                            approvedBy: "$approvedBy",
                            approvedByUser: { $arrayElemAt: ["$approvedByUser", 0] },
                            approvedAt: "$approvedAt",
                        },
                    },
                    flagCount: { $sum: 1 },
                    latestFlagDate: { $max: "$flaggedAt" },
                },
            },
            { $sort: { latestFlagDate: -1 } },
        ]);

        // Get artifact details for each flagged artifact
        const artifactIds = flaggedArtifacts.map(item => item._id);
        if (artifactIds.length === 0) {
            return [];
        }

        const artifacts = await db.qmDev
            .collection("analysis_results")
            .find({ _id: { $in: artifactIds } })
            .toArray();

        // Combine flag data with artifact data
        const result = flaggedArtifacts.map(flagData => {
            const artifact = artifacts.find(a => a._id.toString() === flagData._id.toString());
            return {
                ...artifact,
                flags: flagData.flags,
                flagCount: flagData.flagCount,
                latestFlagDate: flagData.latestFlagDate,
            };
        });

        return result;
    }

    /**
     * Approve a flag (without archiving the artifact)
     * @param {string} flagId - The ID of the flag to approve
     * @param {string} userId - The ID of the user approving the flag
     * @returns {Promise<Object>} The approved flag
     */
    async approveFlag(flagId, userId) {
        // Find the flag
        const flag = await ArtifactFlag.findById(flagId);
        if (!flag) {
            throw new Error("Flag not found");
        }

        if (flag.approvedBy) {
            throw new Error("Flag already approved");
        }

        // Update flag as approved
        flag.approvedBy = mongoose.Types.ObjectId(userId);
        flag.approvedAt = new Date();
        await flag.save();

        return { flag };
    }

    /**
     * Get flags for a specific artifact
     * @param {string} artifactId - The ID of the artifact
     * @returns {Promise<Array>} Array of flags for the artifact
     */
    async getArtifactFlags(artifactId) {
        const flags = await ArtifactFlag.find({
            artifactId: mongoose.Types.ObjectId(artifactId)
        })
        .populate('flaggedBy', 'name email')
        .populate('approvedBy', 'name email')
        .sort({ flaggedAt: -1 });

        return flags;
    }

    /**
     * Check if a user has flagged a specific artifact
     * @param {string} artifactId - The ID of the artifact
     * @param {string} userId - The ID of the user
     * @returns {Promise<boolean>} True if user has flagged the artifact
     */
    async hasUserFlaggedArtifact(artifactId, userId) {
        const flag = await ArtifactFlag.findOne({
            artifactId: mongoose.Types.ObjectId(artifactId),
            flaggedBy: mongoose.Types.ObjectId(userId)
        });

        return !!flag;
    }

    /**
     * Get flag statistics
     * @returns {Promise<Object>} Flag statistics
     */
    async getFlagStatistics() {
        const stats = await ArtifactFlag.aggregate([
            {
                $group: {
                    _id: null,
                    totalFlags: { $sum: 1 },
                    pendingFlags: {
                        $sum: { $cond: [{ $eq: ["$approvedBy", null] }, 1, 0] }
                    },
                    approvedFlags: {
                        $sum: { $cond: [{ $ne: ["$approvedBy", null] }, 1, 0] }
                    },
                    uniqueArtifacts: { $addToSet: "$artifactId" }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalFlags: 1,
                    pendingFlags: 1,
                    approvedFlags: 1,
                    uniqueArtifacts: { $size: "$uniqueArtifacts" }
                }
            }
        ]);

        return stats[0] || {
            totalFlags: 0,
            pendingFlags: 0,
            approvedFlags: 0,
            uniqueArtifacts: 0
        };
    }

    /**
     * Delete a flag (admin only)
     * @param {string} flagId - The ID of the flag to delete
     * @returns {Promise<Object>} The deleted flag
     */
    async deleteFlag(flagId) {
        const flag = await ArtifactFlag.findByIdAndDelete(flagId);
        if (!flag) {
            throw new Error("Flag not found");
        }
        return flag;
    }
}

module.exports = new ArtifactFlagService();
